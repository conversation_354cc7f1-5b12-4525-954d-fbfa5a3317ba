<template>
  <KnowledgeTable :config="tableConfig" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import KnowledgeTable from '../../components/KnowledgeTable.vue';
import type { KnowledgeTableConfig } from '../../composables/useKnowledgeTable';

// 案例优化表格配置
const tableConfig = computed<KnowledgeTableConfig>(() => ({
  promptType: 'SQL_TEMPLATE',
  title: '案例优化',
  searchPlaceholder: '搜索案例优化',
  addButtonText: '新增案例优化',
  columns: [
    {
      prop: 'promptName',
      label: '案例优化',
      minWidth: 150,
      editable: true,
      required: true,
      placeholder: '请输入案例优化标题'
    },
    {
      prop: 'promptContent',
      label: '案例优化描述',
      minWidth: 250,
      editable: true,
      required: true,
      type: 'textarea',
      placeholder: '请输入案例优化描述'
    },
    {
      prop: 'dataSources',
      label: '绑定数据源',
      minWidth: 180,
      type: 'dataSource',
      editable: true
    },
    {
      prop: 'tableName',
      label: '数据表',
      width: 200,
      type: 'table',
      editable: true
    }
  ]
}));
</script>