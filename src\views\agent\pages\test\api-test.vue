<template>
  <div class="api-test-page">
    <div class="test-container">
      <h2>智能体编排API测试</h2>

      <div class="test-section">
        <h3>1. 测试智能体列表API</h3>
        <el-button @click="testAgentsApi" :loading="agentsLoading">
          测试获取智能体列表
        </el-button>
        <div v-if="agentsResult" class="result-box">
          <h4>响应结果:</h4>
          <pre>{{ JSON.stringify(agentsResult, null, 2) }}</pre>
        </div>
        <div v-if="agentsError" class="error-box">
          <h4>错误信息:</h4>
          <pre>{{ agentsError }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>2. 测试编排列表API</h3>
        <el-button @click="testWorkflowListApi" :loading="workflowLoading">
          测试获取编排列表
        </el-button>
        <div v-if="workflowResult" class="result-box">
          <h4>响应结果:</h4>
          <pre>{{ JSON.stringify(workflowResult, null, 2) }}</pre>
        </div>
        <div v-if="workflowError" class="error-box">
          <h4>错误信息:</h4>
          <pre>{{ workflowError }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>3. 测试Token信息</h3>
        <el-button @click="testTokenInfo"> 检查Token状态 </el-button>
        <div v-if="tokenInfo" class="result-box">
          <h4>Token信息:</h4>
          <pre>{{ tokenInfo }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>4. 测试创建编排API</h3>
        <el-button @click="testCreateWorkflow" :loading="createLoading">
          测试创建编排
        </el-button>
        <div v-if="createResult" class="result-box">
          <h4>创建结果:</h4>
          <pre>{{ JSON.stringify(createResult, null, 2) }}</pre>
        </div>
        <div v-if="createError" class="error-box">
          <h4>错误信息:</h4>
          <pre>{{ createError }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import { getAuthToken } from '/@/utils/storage/auth';
  import {
    getAgentsApi,
    getWorkflowListApi,
    createWorkflowApi,
  } from '../../api/agentSet';

  // 智能体API测试
  const agentsLoading = ref(false);
  const agentsResult = ref(null);
  const agentsError = ref('');

  const testAgentsApi = async () => {
    agentsLoading.value = true;
    agentsResult.value = null;
    agentsError.value = '';

    try {
      console.log('开始测试智能体API...');
      const response = await getAgentsApi();
      console.log('智能体API响应:', response);
      agentsResult.value = response;
      ElMessage.success('智能体API测试成功');
    } catch (error) {
      console.error('智能体API测试失败:', error);
      agentsError.value = error.message || error.toString();
      ElMessage.error('智能体API测试失败');
    } finally {
      agentsLoading.value = false;
    }
  };

  // 编排列表API测试
  const workflowLoading = ref(false);
  const workflowResult = ref(null);
  const workflowError = ref('');

  const testWorkflowListApi = async () => {
    workflowLoading.value = true;
    workflowResult.value = null;
    workflowError.value = '';

    try {
      console.log('开始测试编排列表API...');
      const response = await getWorkflowListApi();
      console.log('编排列表API响应:', response);
      workflowResult.value = response;
      ElMessage.success('编排列表API测试成功');
    } catch (error) {
      console.error('编排列表API测试失败:', error);
      workflowError.value = error.message || error.toString();
      ElMessage.error('编排列表API测试失败');
    } finally {
      workflowLoading.value = false;
    }
  };

  // Token信息测试
  const tokenInfo = ref('');

  const testTokenInfo = () => {
    try {
      const token = getAuthToken();
      tokenInfo.value = `Token存在: ${!!token}\nToken长度: ${
        token ? token.length : 0
      }\nToken前10位: ${token ? token.substring(0, 10) + '...' : 'N/A'}`;
      console.log('Token信息:', { hasToken: !!token, tokenLength: token?.length });
    } catch (error) {
      tokenInfo.value = `获取Token失败: ${error.message}`;
      console.error('获取Token失败:', error);
    }
  };

  // 创建编排API测试
  const createLoading = ref(false);
  const createResult = ref(null);
  const createError = ref('');

  const testCreateWorkflow = async () => {
    createLoading.value = true;
    createResult.value = null;
    createError.value = '';

    try {
      console.log('开始测试创建编排API...');
      const testData = {
        title: `API测试编排-${Date.now()}`,
        description: '这是一个API测试创建的编排',
        content: JSON.stringify({
          agentSequence: [
            {
              id: 'agent_1',
              agentId: 'DEFAULT_AGENT',
              agentName: 'DEFAULT_AGENT',
              description: '测试智能体',
              taskDescription: '执行测试任务',
            },
          ],
          metadata: {
            totalAgents: 1,
            configuredAgents: 1,
            createTime: new Date().toISOString(),
            version: '1.0',
          },
        }),
        status: 'draft',
      };

      const response = await createWorkflowApi(testData);
      console.log('创建编排API响应:', response);
      createResult.value = response;
      ElMessage.success('创建编排API测试成功');
    } catch (error) {
      console.error('创建编排API测试失败:', error);
      createError.value = error.message || error.toString();
      ElMessage.error('创建编排API测试失败');
    } finally {
      createLoading.value = false;
    }
  };
</script>

<style scoped lang="scss">
  .api-test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .test-container {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .test-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    h3 {
      color: #333;
      margin-bottom: 16px;
    }
  }

  .result-box {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;

    h4 {
      color: #28a745;
      margin-bottom: 8px;
    }

    pre {
      background: white;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  .error-box {
    margin-top: 16px;
    padding: 16px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;

    h4 {
      color: #721c24;
      margin-bottom: 8px;
    }

    pre {
      background: white;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
      color: #721c24;
    }
  }
</style>
