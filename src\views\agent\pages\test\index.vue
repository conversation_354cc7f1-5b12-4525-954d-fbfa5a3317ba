<template>
  <div class="test-page">
    <h2>功能测试页面</h2>

    <el-card class="test-section">
      <template #header>
        <span>API 测试</span>
      </template>

      <div class="test-buttons">
        <el-button @click="testMcpApi" :loading="loading.mcp"> 测试 MCP API </el-button>
        <el-button @click="testWorkflowApi" :loading="loading.workflow">
          测试 Workflow API
        </el-button>
      </div>

      <div class="test-results">
        <h4>测试结果:</h4>
        <pre>{{ testResults }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ElMessage } from 'element-plus';
  import {
    getMcpServersApi,
    getMcpToolsApi,
    getWorkflowListApi,
  } from '../../api/agentSet';

  const loading = reactive({
    mcp: false,
    workflow: false,
  });

  const testResults = ref('');

  const testMcpApi = async () => {
    loading.mcp = true;
    try {
      const servers = await getMcpServersApi();
      const tools = await getMcpToolsApi();

      testResults.value = JSON.stringify(
        {
          servers,
          tools,
        },
        null,
        2,
      );

      ElMessage.success('MCP API 测试成功');
    } catch (error) {
      testResults.value = `MCP API 错误: ${error.message}`;
      ElMessage.error('MCP API 测试失败');
    } finally {
      loading.mcp = false;
    }
  };

  const testWorkflowApi = async () => {
    loading.workflow = true;
    try {
      const workflows = await getWorkflowListApi();

      testResults.value = JSON.stringify(workflows, null, 2);

      ElMessage.success('Workflow API 测试成功');
    } catch (error) {
      testResults.value = `Workflow API 错误: ${error.message}`;
      ElMessage.error('Workflow API 测试失败');
    } finally {
      loading.workflow = false;
    }
  };
</script>

<style scoped lang="scss">
  .test-page {
    padding: 20px;
  }

  .test-section {
    margin-bottom: 20px;
  }

  .test-buttons {
    margin-bottom: 20px;

    .el-button {
      margin-right: 12px;
    }
  }

  .test-results {
    pre {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      max-height: 400px;
      overflow: auto;
    }
  }
</style>
