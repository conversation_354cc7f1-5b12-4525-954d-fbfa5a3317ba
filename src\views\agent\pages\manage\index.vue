<template>
  <div class="agent-container">
    <div class="main">
      <!-- MCP工具管理  智能体配置  智能体编排 -->

      <div class="sidebar">
        <div class="menu-section">
          <div class="menu-list">
            <div
              v-for="(item, index) in menuList"
              :key="item.id"
              @click="selectItem(index)"
              :class="['menu-item', { active: selectedIndex === index }]"
            >
              <span class="menu-label">{{ item.label }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <!-- <div v-if="selectedIndex === 0">智能体编排 </div>
        <div v-if="selectedIndex === 1"> 智能体配置 </div>
        <div v-if="selectedIndex === 2">工具管理 </div>
        <div v-if="selectedIndex === 3">全局配置 </div> -->
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, shallowRef } from 'vue';
  import AgentSet from './agentSet.vue';
  import Arrange from './arrange.vue';
  import Tools from './tools.vue';
  import GlobalConfig from './globalConfig.vue';

  // const agentSetList = [
  //   {
  //     id: '40',
  //     name: 'DEFAULT_AGENT',
  //     description:
  //       '一个多功能默认代理，可以使用文件操作和shell命令处理各种用户请求。非常适合可能涉及文件操作、系统操作或文本处理的通用任务。',
  //     systemPrompt: ' ',
  //     nextStepPrompt:
  //       '为实现我的目标，下一步应该做什么？\n\n请记住：\n1. 在操作前验证所有输入和路径\n2. 为每个任务选择最合适的工具：\n   - 使用bash进行系统操作\n   - 使用text_file_operator进行文件操作\n   - 任务完成时使用terminate\n3. 优雅地处理错误\n4. 重要：你必须在回复中使用至少一个工具才能取得进展！\n\n逐步思考：\n1. 需要的核心操作是什么？\n2. 哪种工具组合最合适？\n3. 如何处理潜在错误？\n4. 预期的结果是什么？\n5. 如何验证成功？\n\n',
  //     availableTools: ['bash', 'text_file_operator', 'terminate'],
  //     className:
  //       'com.alibaba.cloud.ai.example.manus.dynamic.agent.startupAgent.DDefaultAgent',
  //   },
  //   {
  //     id: '48',
  //     name: 'SQL_EXE_AGENT',
  //     description: 'SQL执行与智能展示专家，基于前序分析提供表格和图表展示方案',
  //     systemPrompt: '',
  //     availableTools: ['bash', 'text_file_operator', 'terminate'],
  //     className:
  //       'com.alibaba.cloud.ai.example.manus.dynamic.agent.startupAgent.DDefaultAgent',
  //   },
  // ];

  const menuList = shallowRef([
    {
      label: '智能体编排',
      value: Arrange,
      id: 1,
    },
    {
      label: '智能体配置',
      value: AgentSet,
      id: 2,
    },
    {
      label: '工具管理',
      value: Tools,
      id: 3,
    },
    {
      label: '全局配置',
      value: GlobalConfig,
      id: 4,
    },
  ]);
  const selectedIndex = ref(1); //   0
  const currentComponent = shallowRef(AgentSet); //Arrange
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
    currentComponent.value = menuList.value[index].value;
  };
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main {
      display: flex;
      flex: 1;
      .sidebar {
        flex: 0 0 220px;
        background: #ffffff;
        border-right: 1px solid #e6e8ee;

        .menu-section {
          padding: 20px 0;

          .menu-list {
            .menu-item {
              display: flex;
              align-items: center;
              padding: 12px 20px;
              margin: 0 12px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.2s ease;
              font-size: 14px;
              font-weight: 500;
              color: #24292f;

              &:hover {
                background: #f6f8fa;
                color: #0969da;
              }

              &.active {
                background: #dbeafe;
                color: #1e40af;
                border-left: 3px solid #3b82f6;
                margin-left: 12px;
                padding-left: 17px;
              }

              .menu-label {
                flex: 1;
              }
            }
          }
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        background: #f6f8fa;
      }
    }
  }
</style>
