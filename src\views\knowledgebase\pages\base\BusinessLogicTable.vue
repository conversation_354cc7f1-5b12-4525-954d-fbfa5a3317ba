<template>
  <KnowledgeTable :config="tableConfig" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import KnowledgeTable from '../../components/KnowledgeTable.vue';
import type { KnowledgeTableConfig } from '../../composables/useKnowledgeTable';

// 业务逻辑表格配置
const tableConfig = computed<KnowledgeTableConfig>(() => ({
  promptType: 'BUSINESS_LOGIC',
  title: '业务逻辑',
  searchPlaceholder: '搜索业务逻辑',
  addButtonText: '新增业务逻辑',
  columns: [
    {
      prop: 'promptName',
      label: '业务逻辑',
      minWidth: 150,
      editable: true,
      required: true,
      placeholder: '请输入业务逻辑标题'
    },
    {
      prop: 'promptContent',
      label: '业务逻辑描述',
      minWidth: 250,
      editable: true,
      required: true,
      type: 'textarea',
      placeholder: '请输入业务逻辑描述'
    },
    {
      prop: 'dataSources',
      label: '绑定数据源',
      minWidth: 180,
      type: 'dataSource',
      editable: true
    },
    {
      prop: 'tableName',
      label: '数据表',
      width: 200,
      type: 'table',
      editable: true
    }
  ]
}));
</script>